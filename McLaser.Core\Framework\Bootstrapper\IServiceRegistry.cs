﻿using System;

namespace McLaser.Core.Framework
{
    public interface IServiceRegistry
    {
        /// <summary>
        /// 注册单例服务
        /// </summary>
        void RegisterSingleton<TService>(TService instance) where TService : class;
        void RegisterSingleton<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService;

        /// <summary>
        /// 注册瞬态服务
        /// </summary>
        void RegisterTransient<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService;

        /// <summary>
        /// 注册作用域服务
        /// </summary>
        void RegisterScoped<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService;

        /// <summary>
        /// 注册服务实例
        /// </summary>
        void RegisterInstance<TService>(TService instance) where TService : class;

        /// <summary>
        /// 注册服务工厂
        /// </summary>
        void RegisterFactory<TService>(Func<IServiceProvider, TService> factory) where TService : class;

        /// <summary>
        /// 配置选项
        /// </summary>
        void ConfigureOptions<T>(Action<T> configure) where T : class, new();
    }

}
