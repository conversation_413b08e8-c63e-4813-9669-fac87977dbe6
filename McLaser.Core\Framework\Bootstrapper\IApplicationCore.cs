﻿using System;

namespace McLaser.Core.Framework
{
    /// <summary>
    /// 应用程序核心接口
    /// </summary>
    public interface IApplicationCore
    {
        /// <summary>
        /// 应用程序唯一标识符
        /// </summary>
        string AppId { get; }

        /// <summary>
        /// 应用程序显示名称
        /// </summary>
        string AppName { get; }

        /// <summary>
        /// 应用程序版本
        /// </summary>
        Version AppVersion { get; }

        /// <summary>
        /// 应用程序启动入口
        /// </summary>
        void Start();

        /// <summary>
        /// 应用程序退出处理
        /// </summary>
        void Shutdown();
    }
}
