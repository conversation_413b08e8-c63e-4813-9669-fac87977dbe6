﻿using System;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Core.Framework
{
    public interface IApplicationCore
    {
        /// <summary>
        /// 应用程序唯一标识符
        /// </summary>
        string AppId { get; }

        /// <summary>
        /// 应用程序显示名称
        /// </summary>
        string AppName { get; }

        /// <summary>
        /// 应用程序版本
        /// </summary>
        Version AppVersion { get; }

        /// <summary>
        /// 应用程序启动入口
        /// </summary>
        void Start();

        /// <summary>
        /// 应用程序退出处理
        /// </summary>
        void Shutdown();

        /// <summary>
        /// 配置应用程序服务
        /// </summary>
        void ConfigureServices(IServiceCollection services);

        /// <summary>
        /// 配置应用程序设置
        /// </summary>
        void ConfigureSettings(IConfigurationBuilder configBuilder);
    }


}
