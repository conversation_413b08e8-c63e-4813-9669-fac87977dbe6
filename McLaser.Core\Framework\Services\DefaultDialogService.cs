using System;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;

namespace McLaser.Core.Framework.Services
{
    /// <summary>
    /// 默认对话框服务实现
    /// 基于WPF MessageBox和文件对话框
    /// </summary>
    public class DefaultDialogService : IDialogService
    {
        /// <summary>
        /// 显示信息对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">对话框标题</param>
        public void ShowMessage(string message, string title = "信息")
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 显示警告对话框
        /// </summary>
        /// <param name="message">警告消息</param>
        /// <param name="title">对话框标题</param>
        public void ShowWarning(string message, string title = "警告")
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// 显示错误对话框
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="title">对话框标题</param>
        public void ShowError(string message, string title = "错误")
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        /// <param name="message">确认消息</param>
        /// <param name="title">对话框标题</param>
        /// <returns>用户选择结果</returns>
        public bool ShowConfirmation(string message, string title = "确认")
        {
            var result = MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
            return result == MessageBoxResult.Yes;
        }

        /// <summary>
        /// 显示异步信息对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">对话框标题</param>
        public Task ShowMessageAsync(string message, string title = "信息")
        {
            return Task.Run(() => ShowMessage(message, title));
        }

        /// <summary>
        /// 显示异步确认对话框
        /// </summary>
        /// <param name="message">确认消息</param>
        /// <param name="title">对话框标题</param>
        /// <returns>用户选择结果</returns>
        public Task<bool> ShowConfirmationAsync(string message, string title = "确认")
        {
            return Task.Run(() => ShowConfirmation(message, title));
        }

        /// <summary>
        /// 显示文件选择对话框
        /// </summary>
        /// <param name="filter">文件过滤器</param>
        /// <param name="title">对话框标题</param>
        /// <returns>选择的文件路径，取消时返回null</returns>
        public string? ShowOpenFileDialog(string filter = "所有文件|*.*", string title = "选择文件")
        {
            var dialog = new OpenFileDialog
            {
                Filter = filter,
                Title = title
            };

            return dialog.ShowDialog() == true ? dialog.FileName : null;
        }

        /// <summary>
        /// 显示文件保存对话框
        /// </summary>
        /// <param name="filter">文件过滤器</param>
        /// <param name="title">对话框标题</param>
        /// <param name="defaultFileName">默认文件名</param>
        /// <returns>保存的文件路径，取消时返回null</returns>
        public string? ShowSaveFileDialog(string filter = "所有文件|*.*", string title = "保存文件", string defaultFileName = "")
        {
            var dialog = new SaveFileDialog
            {
                Filter = filter,
                Title = title,
                FileName = defaultFileName
            };

            return dialog.ShowDialog() == true ? dialog.FileName : null;
        }

        /// <summary>
        /// 显示文件夹选择对话框
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <returns>选择的文件夹路径，取消时返回null</returns>
        public string? ShowFolderBrowserDialog(string title = "选择文件夹")
        {
            // 使用WinForms的文件夹选择对话框
            try
            {
                using (var dialog = new System.Windows.Forms.FolderBrowserDialog())
                {
                    dialog.Description = title;
                    dialog.ShowNewFolderButton = true;

                    return dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK ? dialog.SelectedPath : null;
                }
            }
            catch (Exception)
            {
                // 如果WinForms不可用，返回null
                return null;
            }
        }

        /// <summary>
        /// 显示输入对话框
        /// </summary>
        /// <param name="message">提示消息</param>
        /// <param name="title">对话框标题</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>用户输入的内容，取消时返回null</returns>
        public string? ShowInputDialog(string message, string title = "输入", string defaultValue = "")
        {
            // 使用简单的MessageBox实现输入功能
            // 在生产环境中建议创建自定义输入对话框
            try
            {
                var result = Microsoft.VisualBasic.Interaction.InputBox(message, title, defaultValue);
                return string.IsNullOrEmpty(result) ? null : result;
            }
            catch (Exception)
            {
                // 如果VisualBasic不可用，使用简单的确认对话框
                var confirmed = ShowConfirmation($"{message}\n\n使用默认值: {defaultValue}?", title);
                return confirmed ? defaultValue : null;
            }
        }
    }
}
