using System;

namespace McLaser.Core.Framework.Logging
{
    /// <summary>
    /// 日志记录器接口
    /// </summary>
    public interface ILogger
    {
        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        void Debug(string message);

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        void Debug(string message, params object[] args);

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        void Info(string message);

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        void Info(string message, params object[] args);

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        void Warn(string message);

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        void Warn(string message, params object[] args);

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="message">消息</param>
        void Error(string message);

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        void Error(string message, params object[] args);

        /// <summary>
        /// 记录错误和异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        void Error(Exception exception, string message = "");

        /// <summary>
        /// 记录致命错误
        /// </summary>
        /// <param name="message">消息</param>
        void Fatal(string message);

        /// <summary>
        /// 记录致命错误
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        void Fatal(string message, params object[] args);

        /// <summary>
        /// 记录致命错误和异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        void Fatal(Exception exception, string message = "");

        /// <summary>
        /// 检查是否启用调试级别
        /// </summary>
        bool IsDebugEnabled { get; }

        /// <summary>
        /// 检查是否启用信息级别
        /// </summary>
        bool IsInfoEnabled { get; }

        /// <summary>
        /// 检查是否启用警告级别
        /// </summary>
        bool IsWarnEnabled { get; }

        /// <summary>
        /// 检查是否启用错误级别
        /// </summary>
        bool IsErrorEnabled { get; }

        /// <summary>
        /// 检查是否启用致命错误级别
        /// </summary>
        bool IsFatalEnabled { get; }
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 调试
        /// </summary>
        Debug = 0,

        /// <summary>
        /// 信息
        /// </summary>
        Info = 1,

        /// <summary>
        /// 警告
        /// </summary>
        Warn = 2,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 3,

        /// <summary>
        /// 致命错误
        /// </summary>
        Fatal = 4,

        /// <summary>
        /// 关闭日志
        /// </summary>
        Off = 5
    }

    /// <summary>
    /// 日志工厂接口
    /// </summary>
    public interface ILoggerFactory
    {
        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <param name="name">日志记录器名称</param>
        /// <returns>日志记录器实例</returns>
        ILogger CreateLogger(string name);

        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <returns>日志记录器实例</returns>
        ILogger CreateLogger<T>();

        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>日志记录器实例</returns>
        ILogger CreateLogger(Type type);
    }
}
