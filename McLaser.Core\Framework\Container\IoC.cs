﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.ComponentModel.Composition.Hosting;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;

namespace McLaser.Core.Container
{
    /// <summary>
    /// IoC容器，支持从容器中获取实例
    /// 基于MEF（Managed Extensibility Framework）实现
    /// </summary>
    public class IoC
    {
        private static CompositionContainer? Container;

        static IoC()
        {
            PreInitialize();
        }


        /// <summary>
        /// 预初始化IoC容器，加载所有McLaser相关程序集
        /// </summary>
        private static void PreInitialize()
        {
            try
            {
                // 筛选McLaser相关的DLL文件
                string[] assembliesDllFiles = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory, "*.dll");
                List<string> selectFiles = assembliesDllFiles
                    .Where(item => new FileInfo(item).Name.StartsWith("McLaser", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                // 加载程序集
                var assemblies = selectFiles.Select(x => Assembly.Load(AssemblyName.GetAssemblyName(x)));
                List<Assembly> loadAssembly = new List<Assembly>();
                loadAssembly.AddRange(assemblies);

                // 添加入口程序集
                var entryAssembly = Assembly.GetEntryAssembly();
                if (entryAssembly != null)
                {
                    loadAssembly.Add(entryAssembly);
                }

                var catalog = new AggregateCatalog(loadAssembly.Select(x => new AssemblyCatalog(x)));
                Container = new CompositionContainer(catalog);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("IoC容器初始化失败", ex);
            }
        }


        /// <summary>
        /// 根据Key获取服务
        /// 默认使用泛型类型名称作为契约
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <param name="key">服务键，为空时使用类型名称</param>
        /// <returns>服务实例</returns>
        /// <exception cref="InvalidOperationException">服务未找到时抛出</exception>
        public static T Get<T>(string key = "")
        {
            string contract = string.IsNullOrEmpty(key) ? AttributedModelServices.GetContractName(typeof(T)) : key;
            return GetWrapper<T>(contract);
        }


        /// <summary>
        /// 根据Type和Key获取服务
        /// key为空时使用Type获取服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <param name="key">服务键，为空时使用类型名称</param>
        /// <returns>服务实例</returns>
        /// <exception cref="InvalidOperationException">服务未找到时抛出</exception>
        public static object Get(Type serviceType, string key = "")
        {
            string contract = string.IsNullOrEmpty(key) ? AttributedModelServices.GetContractName(serviceType) : key;
            return GetWrapper(contract);
        }


        /// <summary>
        /// 获取注册为指定类型的所有泛型T服务
        /// </summary>
        /// <typeparam name="T">返回的服务类型</typeparam>
        /// <param name="serviceType">注册的服务类型</param>
        /// <returns>服务实例集合</returns>
        public static IEnumerable<T> GetAll<T>(Type serviceType)
        {
            if (Container == null)
                throw new InvalidOperationException("IoC容器未初始化");

            return Container.GetExportedValues<T>(AttributedModelServices.GetContractName(serviceType));
        }


        /// <summary>
        /// 获取注册为指定类型的所有服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例集合</returns>
        public static IEnumerable<object> GetAll(Type serviceType)
        {
            if (Container == null)
                throw new InvalidOperationException("IoC容器未初始化");

            return Container.GetExportedValues<object>(AttributedModelServices.GetContractName(serviceType));
        }


        /// <summary>
        /// 获取所有注册为泛型T的服务
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例集合</returns>
        public static IEnumerable<T> GetAll<T>()
        {
            if (Container == null)
                throw new InvalidOperationException("IoC容器未初始化");

            return Container.GetExportedValues<T>(AttributedModelServices.GetContractName(typeof(T)));
        }


        /// <summary>
        /// 获取泛型服务包装器
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <param name="contract">服务契约</param>
        /// <returns>服务实例</returns>
        private static T GetWrapper<T>(string contract)
        {
            var result = GetWrapper(contract);
            if (result is T typedResult)
                return typedResult;

            throw new InvalidOperationException($"服务 {contract} 无法转换为类型 {typeof(T).Name}");
        }

        /// <summary>
        /// 获取服务包装器
        /// </summary>
        /// <param name="contract">服务契约</param>
        /// <returns>服务实例</returns>
        private static object GetWrapper(string contract)
        {
            if (Container == null)
                throw new InvalidOperationException("IoC容器未初始化");

            try
            {
                var exports = Container.GetExports<object>(contract);
                if (exports.Any())
                    return exports.First().Value;
            }
            catch (ReflectionTypeLoadException ex)
            {
                var errorMessage = BuildReflectionErrorMessage(ex);
                throw new InvalidOperationException($"加载程序集时发生错误: {errorMessage}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"获取服务 {contract} 时发生错误", ex);
            }

            throw new InvalidOperationException($"未找到契约为 {contract} 的服务实例");
        }

        /// <summary>
        /// 构建反射错误消息
        /// </summary>
        /// <param name="ex">反射类型加载异常</param>
        /// <returns>错误消息</returns>
        private static string BuildReflectionErrorMessage(ReflectionTypeLoadException ex)
        {
            var sb = new StringBuilder();
            sb.AppendLine("程序集加载失败:");

            foreach (Exception exSub in ex.LoaderExceptions)
            {
                sb.AppendLine($"- {exSub.Message}");

                if (exSub is FileNotFoundException fileNotFound &&
                    !string.IsNullOrEmpty(fileNotFound.FusionLog))
                {
                    sb.AppendLine("  Fusion Log:");
                    sb.AppendLine($"  {fileNotFound.FusionLog}");
                }
            }

            return sb.ToString();
        }

    }
}
