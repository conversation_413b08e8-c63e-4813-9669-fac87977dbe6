﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.ComponentModel.Composition.Hosting;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Remoting.Metadata.W3cXsd2001;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Core.Container
{
     /// <summary>
     /// IoC容器，支持从容器中获取实例
     /// </summary>
    public class IoC 
    {
        private static CompositionContainer Container;

         
        public IoC()
        {
            PreInitialize();
        }


        private static void PreInitialize()
        {
            //筛选
            string[] assembliesDllFile = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory, "*.dll");
            List<string> selectFiles = assembliesDllFile.Where(item => new FileInfo(item).Name.StartsWith("McLaser")).ToList();

            //加载
            var assemblies = selectFiles.Select(x => Assembly.Load(AssemblyName.GetAssemblyName(x)));
            List<Assembly> loadAssemly = new List<Assembly>();
            loadAssemly.AddRange(assemblies);
            //入口
            loadAssemly.Add(Assembly.GetEntryAssembly());
            var catalog = new AggregateCatalog(loadAssemly.Select(x => new AssemblyCatalog(x)));
            Container = new CompositionContainer(catalog);
        }


        /// <summary>
        /// 根据Key获取服务
        /// 默认泛型类型名称
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static T Get<T>(string key = "")
        {
            string contract = string.IsNullOrEmpty(key) ? AttributedModelServices.GetContractName(typeof(T)) : key;
            return IoC.GetWapper<T>(contract);
        }


        /// <summary>
        /// 根据Key获取服务 
        /// key 为空值的情况下  使用Type获取服务
        /// </summary>
        /// <param name="serviceType"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static object Get(Type serviceType, string key = "")
        {
            string contract = string.IsNullOrEmpty(key) ? AttributedModelServices.GetContractName(serviceType) : key;
            return IoC.GetWapper(contract);
        }


        /// <summary>
        /// 获取注册为Type类型的所有泛型T服务
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static IEnumerable<T> GetAll<T>(Type t)
        {
            return IoC.Container.GetExportedValues<T>(AttributedModelServices.GetContractName(t));
        }


        /// <summary>
        /// 获取注册Type类型所有服务
        /// </summary>
        /// <param name="serviceType"></param>
        /// <returns></returns>
        public static IEnumerable<object> GetAll(Type serviceType)
        {
            return IoC.Container.GetExportedValues<object>(AttributedModelServices.GetContractName(serviceType));
        }


        /// <summary>
        /// 获取所有注册为泛型T的服务
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static IEnumerable<T> GetAll<T>()
        {
            return IoC.Container.GetExportedValues<T>(AttributedModelServices.GetContractName(typeof(T)));
        }


        private static T GetWapper<T>(string contract)
        {
            try
            {
                var exports = Container.GetExports<T>(contract);
                if (exports.Any())
                    return (T)exports.First().Value;
            }
            catch (ReflectionTypeLoadException ex)
            {
                StringBuilder sb = new StringBuilder();
                foreach (Exception exSub in ex.LoaderExceptions)
                {
                    sb.AppendLine(exSub.Message);
                    FileNotFoundException exFileNotFound = exSub as FileNotFoundException;
                    if (exFileNotFound != null)
                    {
                        if (!string.IsNullOrEmpty(exFileNotFound.FusionLog))
                        {
                            sb.AppendLine("Fusion Log:");
                            sb.AppendLine(exFileNotFound.FusionLog);
                        }
                    }
                    sb.AppendLine();
                }
                Console.WriteLine(sb);
            }
            throw new Exception(string.Format("Could not locate any instances of contract {0}.", contract));
        }


        private static object GetWapper(string contract)
        {
            try
            {
                var exports = Container.GetExports<object>(contract);
                if (exports.Any())
                    return exports.First().Value;
            }
            catch (ReflectionTypeLoadException ex)
            {
                StringBuilder sb = new StringBuilder();
                foreach (Exception exSub in ex.LoaderExceptions)
                {
                    sb.AppendLine(exSub.Message);
                    FileNotFoundException exFileNotFound = exSub as FileNotFoundException;
                    if (exFileNotFound != null)
                    {
                        if (!string.IsNullOrEmpty(exFileNotFound.FusionLog))
                        {
                            sb.AppendLine("Fusion Log:");
                            sb.AppendLine(exFileNotFound.FusionLog);
                        }
                    }
                    sb.AppendLine();
                }
                Console.WriteLine(sb);
            }
            throw new Exception(string.Format("Could not locate any instances of contract {0}.", contract));
        }

    }
}
