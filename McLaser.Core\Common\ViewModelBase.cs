using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace McLaser.Core.Common
{
    /// <summary>
    /// ViewModel基类，实现INotifyPropertyChanged接口
    /// 为WPF数据绑定提供属性变更通知支持
    /// </summary>
    public abstract class ViewModelBase : INotifyPropertyChanged, IDisposable
    {
        private bool _isDisposed;

        /// <summary>
        /// 属性变更事件
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        /// <param name="propertyName">属性名称，自动获取调用者名称</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 设置属性值并触发变更通知
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="field">字段引用</param>
        /// <param name="value">新值</param>
        /// <param name="propertyName">属性名称，自动获取调用者名称</param>
        /// <returns>如果值发生变化返回true，否则返回false</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 设置属性值并触发变更通知（带回调）
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="field">字段引用</param>
        /// <param name="value">新值</param>
        /// <param name="onChanged">值变更后的回调</param>
        /// <param name="propertyName">属性名称，自动获取调用者名称</param>
        /// <returns>如果值发生变化返回true，否则返回false</returns>
        protected bool SetProperty<T>(ref T field, T value, Action onChanged, [CallerMemberName] string? propertyName = null)
        {
            if (SetProperty(ref field, value, propertyName))
            {
                onChanged?.Invoke();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 验证属性名称是否有效
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <returns>如果属性存在返回true，否则返回false</returns>
        protected bool IsValidPropertyName(string propertyName)
        {
            return TypeDescriptor.GetProperties(this)[propertyName] != null;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed && disposing)
            {
                // 子类可以重写此方法来释放特定资源
                OnDisposing();
                _isDisposed = true;
            }
        }

        /// <summary>
        /// 释放资源时调用，子类可重写
        /// </summary>
        protected virtual void OnDisposing()
        {
            // 默认实现为空，子类可以重写
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~ViewModelBase()
        {
            Dispose(false);
        }
    }
}
