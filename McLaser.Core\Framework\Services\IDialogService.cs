using System;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Services
{
    /// <summary>
    /// 对话框服务接口
    /// 提供统一的对话框显示功能
    /// </summary>
    public interface IDialogService
    {
        /// <summary>
        /// 显示信息对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">对话框标题</param>
        void ShowMessage(string message, string title = "信息");

        /// <summary>
        /// 显示警告对话框
        /// </summary>
        /// <param name="message">警告消息</param>
        /// <param name="title">对话框标题</param>
        void ShowWarning(string message, string title = "警告");

        /// <summary>
        /// 显示错误对话框
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="title">对话框标题</param>
        void ShowError(string message, string title = "错误");

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        /// <param name="message">确认消息</param>
        /// <param name="title">对话框标题</param>
        /// <returns>用户选择结果</returns>
        bool ShowConfirmation(string message, string title = "确认");

        /// <summary>
        /// 显示异步信息对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">对话框标题</param>
        Task ShowMessageAsync(string message, string title = "信息");

        /// <summary>
        /// 显示异步确认对话框
        /// </summary>
        /// <param name="message">确认消息</param>
        /// <param name="title">对话框标题</param>
        /// <returns>用户选择结果</returns>
        Task<bool> ShowConfirmationAsync(string message, string title = "确认");

        /// <summary>
        /// 显示文件选择对话框
        /// </summary>
        /// <param name="filter">文件过滤器</param>
        /// <param name="title">对话框标题</param>
        /// <returns>选择的文件路径，取消时返回null</returns>
        string? ShowOpenFileDialog(string filter = "所有文件|*.*", string title = "选择文件");

        /// <summary>
        /// 显示文件保存对话框
        /// </summary>
        /// <param name="filter">文件过滤器</param>
        /// <param name="title">对话框标题</param>
        /// <param name="defaultFileName">默认文件名</param>
        /// <returns>保存的文件路径，取消时返回null</returns>
        string? ShowSaveFileDialog(string filter = "所有文件|*.*", string title = "保存文件", string defaultFileName = "");

        /// <summary>
        /// 显示文件夹选择对话框
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <returns>选择的文件夹路径，取消时返回null</returns>
        string? ShowFolderBrowserDialog(string title = "选择文件夹");

        /// <summary>
        /// 显示输入对话框
        /// </summary>
        /// <param name="message">提示消息</param>
        /// <param name="title">对话框标题</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>用户输入的内容，取消时返回null</returns>
        string? ShowInputDialog(string message, string title = "输入", string defaultValue = "");
    }

    /// <summary>
    /// 对话框结果枚举
    /// </summary>
    public enum DialogResult
    {
        /// <summary>
        /// 确定
        /// </summary>
        OK,
        
        /// <summary>
        /// 取消
        /// </summary>
        Cancel,
        
        /// <summary>
        /// 是
        /// </summary>
        Yes,
        
        /// <summary>
        /// 否
        /// </summary>
        No
    }

    /// <summary>
    /// 消息类型枚举
    /// </summary>
    public enum MessageType
    {
        /// <summary>
        /// 信息
        /// </summary>
        Information,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        
        /// <summary>
        /// 确认
        /// </summary>
        Question
    }
}
