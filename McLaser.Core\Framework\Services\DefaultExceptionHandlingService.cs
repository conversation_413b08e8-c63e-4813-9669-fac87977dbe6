using System;
using System.Collections.Generic;
using System.IO;
using McLaser.Core.Framework.Logging;

namespace McLaser.Core.Framework.Services
{
    /// <summary>
    /// 默认异常处理服务实现
    /// </summary>
    public class DefaultExceptionHandlingService : IExceptionHandlingService
    {
        private readonly Dictionary<Type, IExceptionHandler> _handlers = new();
        private readonly ILogger? _logger;
        private readonly IDialogService? _dialogService;

        /// <summary>
        /// 异常发生事件
        /// </summary>
        public event EventHandler<ExceptionEventArgs>? ExceptionOccurred;

        /// <summary>
        /// 初始化异常处理服务
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="dialogService">对话框服务</param>
        public DefaultExceptionHandlingService(ILogger? logger = null, IDialogService? dialogService = null)
        {
            _logger = logger;
            _dialogService = dialogService;

            // 注册默认异常处理器
            RegisterDefaultHandlers();
        }

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>是否已处理</returns>
        public bool HandleException(Exception exception, string? context = null)
        {
            if (exception == null)
                throw new ArgumentNullException(nameof(exception));

            var eventArgs = new ExceptionEventArgs(exception, context);
            
            try
            {
                // 触发异常发生事件
                OnExceptionOccurred(eventArgs);

                // 如果事件处理器已经处理了异常，直接返回
                if (eventArgs.Result?.IsHandled == true)
                {
                    return true;
                }

                // 查找特定的异常处理器
                var handler = FindHandler(exception.GetType());
                if (handler != null)
                {
                    var result = handler.Handle(exception, context);
                    eventArgs.Result = result;

                    if (result.ShouldLog && _logger != null)
                    {
                        _logger.Error(exception, context ?? "未处理的异常");
                    }

                    if (!string.IsNullOrEmpty(result.UserMessage) && _dialogService != null)
                    {
                        _dialogService.ShowError(result.UserMessage!, "错误");
                    }

                    return result.IsHandled;
                }

                // 使用默认处理逻辑
                return HandleDefaultException(exception, context);
            }
            catch (Exception handlingException)
            {
                // 异常处理过程中发生异常，记录并返回false
                _logger?.Fatal(handlingException, "异常处理过程中发生错误");
                return false;
            }
        }

        /// <summary>
        /// 处理异常并显示用户友好的消息
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="userMessage">用户友好的消息</param>
        /// <param name="context">异常上下文</param>
        /// <returns>是否已处理</returns>
        public bool HandleExceptionWithUserMessage(Exception exception, string userMessage, string? context = null)
        {
            if (exception == null)
                throw new ArgumentNullException(nameof(exception));

            if (string.IsNullOrEmpty(userMessage))
                throw new ArgumentException("用户消息不能为空", nameof(userMessage));

            // 记录异常
            _logger?.Error(exception, context ?? "用户操作异常");

            // 显示用户友好的消息
            _dialogService?.ShowError(userMessage, "操作失败");

            // 触发异常事件
            var eventArgs = new ExceptionEventArgs(exception, context)
            {
                Result = ExceptionHandlingResult.Handled(userMessage)
            };
            OnExceptionOccurred(eventArgs);

            return true;
        }

        /// <summary>
        /// 记录异常但不显示给用户
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        public void LogException(Exception exception, string? context = null)
        {
            if (exception == null)
                throw new ArgumentNullException(nameof(exception));

            _logger?.Error(exception, context ?? "记录的异常");

            // 触发异常事件但不显示给用户
            var eventArgs = new ExceptionEventArgs(exception, context)
            {
                Result = ExceptionHandlingResult.Handled()
            };
            OnExceptionOccurred(eventArgs);
        }

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <param name="handler">异常处理器</param>
        public void RegisterHandler<T>(IExceptionHandler<T> handler) where T : Exception
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            _handlers[typeof(T)] = handler;
        }

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <param name="handler">异常处理器</param>
        public void RegisterHandler(Type exceptionType, IExceptionHandler handler)
        {
            if (exceptionType == null)
                throw new ArgumentNullException(nameof(exceptionType));
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));
            if (!typeof(Exception).IsAssignableFrom(exceptionType))
                throw new ArgumentException("类型必须继承自Exception", nameof(exceptionType));

            _handlers[exceptionType] = handler;
        }

        /// <summary>
        /// 移除异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        public void RemoveHandler<T>() where T : Exception
        {
            _handlers.Remove(typeof(T));
        }

        /// <summary>
        /// 移除异常处理器
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        public void RemoveHandler(Type exceptionType)
        {
            if (exceptionType != null)
            {
                _handlers.Remove(exceptionType);
            }
        }

        /// <summary>
        /// 查找异常处理器
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>异常处理器</returns>
        private IExceptionHandler? FindHandler(Type exceptionType)
        {
            // 首先查找精确匹配
            if (_handlers.TryGetValue(exceptionType, out var handler))
            {
                return handler;
            }

            // 查找基类匹配
            var currentType = exceptionType.BaseType;
            while (currentType != null && typeof(Exception).IsAssignableFrom(currentType))
            {
                if (_handlers.TryGetValue(currentType, out handler))
                {
                    return handler;
                }
                currentType = currentType.BaseType;
            }

            return null;
        }

        /// <summary>
        /// 处理默认异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">上下文</param>
        /// <returns>是否已处理</returns>
        private bool HandleDefaultException(Exception exception, string? context)
        {
            // 记录异常
            _logger?.Error(exception, context ?? "未处理的异常");

            // 显示通用错误消息
            var userMessage = GetUserFriendlyMessage(exception);
            _dialogService?.ShowError(userMessage, "系统错误");

            return true;
        }

        /// <summary>
        /// 获取用户友好的错误消息
        /// </summary>
        /// <param name="exception">异常</param>
        /// <returns>用户友好的消息</returns>
        private static string GetUserFriendlyMessage(Exception exception)
        {
            return exception switch
            {
                ArgumentException => "输入参数无效，请检查输入内容。",
                InvalidOperationException => "当前操作无效，请稍后重试。",
                UnauthorizedAccessException => "没有权限执行此操作。",
                FileNotFoundException => "找不到指定的文件。",
                DirectoryNotFoundException => "找不到指定的目录。",
                OutOfMemoryException => "系统内存不足，请关闭一些应用程序后重试。",
                TimeoutException => "操作超时，请检查网络连接后重试。",
                _ => "系统发生未知错误，请联系技术支持。"
            };
        }

        /// <summary>
        /// 注册默认异常处理器
        /// </summary>
        private void RegisterDefaultHandlers()
        {
            // 可以在这里注册一些常见异常的默认处理器
            // 例如：RegisterHandler<ArgumentException>(new ArgumentExceptionHandler());
        }

        /// <summary>
        /// 触发异常发生事件
        /// </summary>
        /// <param name="args">事件参数</param>
        protected virtual void OnExceptionOccurred(ExceptionEventArgs args)
        {
            ExceptionOccurred?.Invoke(this, args);
        }
    }
}
