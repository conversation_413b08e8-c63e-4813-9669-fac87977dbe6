﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;

namespace McLaser.Core.Framework
{
    public class DefaultServiceRegistry : IServiceRegistry, IServiceProvider, IDisposable
    {
        private readonly Dictionary<Type, ServiceRegistration> _registrations = new();
        private readonly Dictionary<Type, object> _instances = new();
        private readonly List<Action<IServiceProvider>> _configActions = new();

        public void RegisterSingleton<TService>(TService instance) where TService : class
        {
            _instances[typeof(TService)] = instance;
        }

        public void RegisterSingleton<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            _registrations[typeof(TService)] = new ServiceRegistration(
                typeof(TImplementation),
                ServiceLifetime.Singleton);
        }

        public void RegisterTransient<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            _registrations[typeof(TService)] = new ServiceRegistration(
                typeof(TImplementation),
                ServiceLifetime.Transient);
        }

        public void RegisterScoped<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            _registrations[typeof(TService)] = new ServiceRegistration(
                typeof(TImplementation),
                ServiceLifetime.Scoped);
        }

        public void RegisterInstance<TService>(TService instance) where TService : class
        {
            _instances[typeof(TService)] = instance;
        }

        public void RegisterFactory<TService>(Func<IServiceProvider, TService> factory) where TService : class
        {
            _registrations[typeof(TService)] = new ServiceRegistration(
                factory,
                ServiceLifetime.Transient); // 默认为瞬态
        }

        public void ConfigureOptions<T>(Action<T> configure) where T : class, new()
        {
            _configActions.Add(provider =>
            {
                var options = new T();
                configure(options);
                _instances[typeof(T)] = options;
            });
        }

        public IServiceProvider BuildServiceProvider()
        {
            // 执行配置操作
            _configActions.ForEach(action => action(this));
            return this;
        }

        public T GetService<T>() where T : class
        {
            var serviceType = typeof(T);

            // 首先检查实例
            if (_instances.TryGetValue(serviceType, out var instance))
            {
                return (T)instance;
            }

            // 检查注册
            if (_registrations.TryGetValue(serviceType, out var registration))
            {
                return (T)CreateService(registration);
            }

            throw new InvalidOperationException($"未注册的服务: {serviceType.Name}");
        }

        public T? GetOptionalService<T>() where T : class
        {
            try
            {
                return GetService<T>();
            }
            catch
            {
                return null;
            }
        }

        private object CreateService(ServiceRegistration registration)
        {
            // 工厂创建
            if (registration.Factory != null)
            {
                return registration.Factory(this);
            }

            // 类型创建
            var implementationType = registration.ImplementationType;
            var constructors = implementationType.GetConstructors();

            if (constructors.Length == 0)
            {
                throw new InvalidOperationException(
                    $"类型 {implementationType.Name} 没有公共构造函数");
            }

            // 使用第一个构造函数
            var constructor = constructors[0];
            var parameters = constructor.GetParameters();
            var args = new object[parameters.Length];

            for (int i = 0; i < parameters.Length; i++)
            {
                var paramType = parameters[i].ParameterType;
                args[i] = GetService(paramType);
            }

            return Activator.CreateInstance(implementationType, args)!;
        }

        public void Dispose()
        {
            foreach (var instance in _instances.Values.OfType<IDisposable>())
            {
                instance.Dispose();
            }
            _instances.Clear();
            _registrations.Clear();
        }

        private class ServiceRegistration
        {
            public Type? ImplementationType { get; }
            public Func<IServiceProvider, object>? Factory { get; }
            public ServiceLifetime Lifetime { get; }

            public ServiceRegistration(
                Type implementationType,
                ServiceLifetime lifetime)
            {
                ImplementationType = implementationType;
                Lifetime = lifetime;
            }

            public ServiceRegistration(
                Func<IServiceProvider, object> factory,
                ServiceLifetime lifetime)
            {
                Factory = factory;
                Lifetime = lifetime;
            }
        }

        private enum ServiceLifetime
        {
            Singleton,
            Scoped,
            Transient
        }

        /// <summary>
        /// 实现非泛型GetService，优化性能
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        public object GetService(Type serviceType)
        {
            // 首先检查实例
            if (_instances.TryGetValue(serviceType, out var instance))
            {
                return instance;
            }

            // 检查注册
            if (_registrations.TryGetValue(serviceType, out var registration))
            {
                return CreateService(registration);
            }

            throw new InvalidOperationException($"未注册的服务: {serviceType.Name}");
        }
    }


}
