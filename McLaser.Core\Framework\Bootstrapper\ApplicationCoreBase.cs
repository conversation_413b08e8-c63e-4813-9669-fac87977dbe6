﻿#nullable enable
using System;
using System.Windows;

namespace McLaser.Core.Framework
{
    public abstract class ApplicationCoreBase : IDisposable
    {
        protected IServiceProvider? ServiceProvider { get; private set; }

        // 抽象属性 - 必须由子类实现
        public abstract string AppId { get; }
        public abstract string AppName { get; }
        public abstract Version AppVersion { get; }

        /// <summary>
        /// 配置服务注册 - 必须由子类实现
        /// </summary>
        protected abstract void ConfigureServices(IServiceRegistry services);

        /// <summary>
        /// 配置应用程序设置 - 可选覆盖
        /// </summary>
        protected virtual void ConfigureSettings()
        {
            // 默认配置实现
        }

        public virtual void Start()
        {
            // 1. 创建服务注册器
            var registry = new DefaultServiceRegistry();

            // 2. 配置设置
            ConfigureSettings();

            // 3. 注册核心服务
            registry.RegisterSingleton<IApplicationCore>(this);
            registry.RegisterSingleton<ApplicationService>();

            // 4. 子类配置服务
            ConfigureServices(registry);

            // 5. 构建服务提供者
            ServiceProvider = registry.BuildServiceProvider();

            // 6. 创建主窗口
            var mainWindow = CreateMainWindow();
            mainWindow.Show();
        }

        public virtual void Shutdown()
        {
            // 清理资源
            if (ServiceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }

        /// <summary>
        /// 创建应用程序主窗口 - 必须由子类实现
        /// </summary>
        protected abstract Window CreateMainWindow();

        /// <summary>
        /// 获取服务
        /// </summary>
        protected T GetService<T>() where T : class
        {
            return ServiceProvider?.GetService<T>() ??
                throw new InvalidOperationException("服务提供者未初始化");
        }

        public void Dispose()
        {
            Shutdown();
            GC.SuppressFinalize(this);
        }
    }
}
