using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Data
{
    /// <summary>
    /// 通用仓储接口
    /// 提供标准的CRUD操作和查询功能
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public interface IRepository<T> where T : class
    {
        /// <summary>
        /// 根据ID获取实体
        /// </summary>
        /// <param name="id">实体ID</param>
        /// <returns>实体对象</returns>
        T? GetById(object id);

        /// <summary>
        /// 异步根据ID获取实体
        /// </summary>
        /// <param name="id">实体ID</param>
        /// <returns>实体对象</returns>
        Task<T?> GetByIdAsync(object id);

        /// <summary>
        /// 获取所有实体
        /// </summary>
        /// <returns>实体集合</returns>
        IEnumerable<T> GetAll();

        /// <summary>
        /// 异步获取所有实体
        /// </summary>
        /// <returns>实体集合</returns>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// 根据条件查找实体
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>实体集合</returns>
        IEnumerable<T> Find(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// 异步根据条件查找实体
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>实体集合</returns>
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// 根据条件获取单个实体
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>实体对象</returns>
        T? SingleOrDefault(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// 异步根据条件获取单个实体
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>实体对象</returns>
        Task<T?> SingleOrDefaultAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// 添加实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        void Add(T entity);

        /// <summary>
        /// 异步添加实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        Task AddAsync(T entity);

        /// <summary>
        /// 批量添加实体
        /// </summary>
        /// <param name="entities">实体集合</param>
        void AddRange(IEnumerable<T> entities);

        /// <summary>
        /// 异步批量添加实体
        /// </summary>
        /// <param name="entities">实体集合</param>
        Task AddRangeAsync(IEnumerable<T> entities);

        /// <summary>
        /// 更新实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        void Update(T entity);

        /// <summary>
        /// 异步更新实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        Task UpdateAsync(T entity);

        /// <summary>
        /// 删除实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        void Remove(T entity);

        /// <summary>
        /// 根据ID删除实体
        /// </summary>
        /// <param name="id">实体ID</param>
        void Remove(object id);

        /// <summary>
        /// 异步删除实体
        /// </summary>
        /// <param name="entity">实体对象</param>
        Task RemoveAsync(T entity);

        /// <summary>
        /// 异步根据ID删除实体
        /// </summary>
        /// <param name="id">实体ID</param>
        Task RemoveAsync(object id);

        /// <summary>
        /// 批量删除实体
        /// </summary>
        /// <param name="entities">实体集合</param>
        void RemoveRange(IEnumerable<T> entities);

        /// <summary>
        /// 异步批量删除实体
        /// </summary>
        /// <param name="entities">实体集合</param>
        Task RemoveRangeAsync(IEnumerable<T> entities);

        /// <summary>
        /// 检查实体是否存在
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>是否存在</returns>
        bool Exists(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// 异步检查实体是否存在
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// 获取实体数量
        /// </summary>
        /// <returns>实体数量</returns>
        int Count();

        /// <summary>
        /// 根据条件获取实体数量
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>实体数量</returns>
        int Count(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// 异步获取实体数量
        /// </summary>
        /// <returns>实体数量</returns>
        Task<int> CountAsync();

        /// <summary>
        /// 异步根据条件获取实体数量
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>实体数量</returns>
        Task<int> CountAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="pageIndex">页索引（从0开始）</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>分页结果</returns>
        IPagedResult<T> GetPaged(int pageIndex, int pageSize);

        /// <summary>
        /// 根据条件分页查询
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <param name="pageIndex">页索引（从0开始）</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>分页结果</returns>
        IPagedResult<T> GetPaged(Expression<Func<T, bool>> predicate, int pageIndex, int pageSize);

        /// <summary>
        /// 异步分页查询
        /// </summary>
        /// <param name="pageIndex">页索引（从0开始）</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>分页结果</returns>
        Task<IPagedResult<T>> GetPagedAsync(int pageIndex, int pageSize);

        /// <summary>
        /// 异步根据条件分页查询
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <param name="pageIndex">页索引（从0开始）</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>分页结果</returns>
        Task<IPagedResult<T>> GetPagedAsync(Expression<Func<T, bool>> predicate, int pageIndex, int pageSize);
    }

    /// <summary>
    /// 分页结果接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public interface IPagedResult<T>
    {
        /// <summary>
        /// 当前页数据
        /// </summary>
        IEnumerable<T> Items { get; }

        /// <summary>
        /// 总记录数
        /// </summary>
        int TotalCount { get; }

        /// <summary>
        /// 页索引（从0开始）
        /// </summary>
        int PageIndex { get; }

        /// <summary>
        /// 页大小
        /// </summary>
        int PageSize { get; }

        /// <summary>
        /// 总页数
        /// </summary>
        int TotalPages { get; }

        /// <summary>
        /// 是否有上一页
        /// </summary>
        bool HasPreviousPage { get; }

        /// <summary>
        /// 是否有下一页
        /// </summary>
        bool HasNextPage { get; }
    }

    /// <summary>
    /// 分页结果实现
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public class PagedResult<T> : IPagedResult<T>
    {
        public IEnumerable<T> Items { get; }
        public int TotalCount { get; }
        public int PageIndex { get; }
        public int PageSize { get; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageIndex > 0;
        public bool HasNextPage => PageIndex < TotalPages - 1;

        public PagedResult(IEnumerable<T> items, int totalCount, int pageIndex, int pageSize)
        {
            Items = items ?? throw new ArgumentNullException(nameof(items));
            TotalCount = totalCount;
            PageIndex = pageIndex;
            PageSize = pageSize;
        }
    }
}
