using System;

namespace McLaser.Core.Framework.Services
{
    /// <summary>
    /// 异常处理服务接口
    /// 提供统一的异常处理和报告功能
    /// </summary>
    public interface IExceptionHandlingService
    {
        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>是否已处理</returns>
        bool HandleException(Exception exception, string? context = null);

        /// <summary>
        /// 处理异常并显示用户友好的消息
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="userMessage">用户友好的消息</param>
        /// <param name="context">异常上下文</param>
        /// <returns>是否已处理</returns>
        bool HandleExceptionWithUserMessage(Exception exception, string userMessage, string? context = null);

        /// <summary>
        /// 记录异常但不显示给用户
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        void LogException(Exception exception, string? context = null);

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <param name="handler">异常处理器</param>
        void RegisterHandler<T>(IExceptionHandler<T> handler) where T : Exception;

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <param name="handler">异常处理器</param>
        void RegisterHandler(Type exceptionType, IExceptionHandler handler);

        /// <summary>
        /// 移除异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        void RemoveHandler<T>() where T : Exception;

        /// <summary>
        /// 移除异常处理器
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        void RemoveHandler(Type exceptionType);

        /// <summary>
        /// 异常发生事件
        /// </summary>
        event EventHandler<ExceptionEventArgs>? ExceptionOccurred;
    }

    /// <summary>
    /// 异常处理器接口
    /// </summary>
    public interface IExceptionHandler
    {
        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>异常处理结果</returns>
        ExceptionHandlingResult Handle(Exception exception, string? context = null);
    }

    /// <summary>
    /// 泛型异常处理器接口
    /// </summary>
    /// <typeparam name="T">异常类型</typeparam>
    public interface IExceptionHandler<in T> : IExceptionHandler where T : Exception
    {
        /// <summary>
        /// 处理特定类型的异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>异常处理结果</returns>
        ExceptionHandlingResult Handle(T exception, string? context = null);
    }

    /// <summary>
    /// 异常处理结果
    /// </summary>
    public class ExceptionHandlingResult
    {
        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsHandled { get; set; }

        /// <summary>
        /// 用户消息
        /// </summary>
        public string? UserMessage { get; set; }

        /// <summary>
        /// 是否应该重新抛出异常
        /// </summary>
        public bool ShouldRethrow { get; set; }

        /// <summary>
        /// 是否应该记录异常
        /// </summary>
        public bool ShouldLog { get; set; } = true;

        /// <summary>
        /// 创建已处理的结果
        /// </summary>
        /// <param name="userMessage">用户消息</param>
        /// <returns>处理结果</returns>
        public static ExceptionHandlingResult Handled(string? userMessage = null)
        {
            return new ExceptionHandlingResult
            {
                IsHandled = true,
                UserMessage = userMessage,
                ShouldRethrow = false
            };
        }

        /// <summary>
        /// 创建未处理的结果
        /// </summary>
        /// <param name="shouldRethrow">是否重新抛出</param>
        /// <returns>处理结果</returns>
        public static ExceptionHandlingResult NotHandled(bool shouldRethrow = true)
        {
            return new ExceptionHandlingResult
            {
                IsHandled = false,
                ShouldRethrow = shouldRethrow
            };
        }
    }

    /// <summary>
    /// 异常事件参数
    /// </summary>
    public class ExceptionEventArgs : EventArgs
    {
        /// <summary>
        /// 异常
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// 异常上下文
        /// </summary>
        public string? Context { get; }

        /// <summary>
        /// 处理结果
        /// </summary>
        public ExceptionHandlingResult? Result { get; set; }

        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime Timestamp { get; }

        public ExceptionEventArgs(Exception exception, string? context = null)
        {
            Exception = exception;
            Context = context;
            Timestamp = DateTime.Now;
        }
    }
}
