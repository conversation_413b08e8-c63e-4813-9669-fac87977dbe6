using System;
using System.Collections.Generic;

namespace McLaser.Core.Framework.Configuration
{
    /// <summary>
    /// 配置服务接口
    /// 提供应用程序配置的读取和保存功能
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        T GetValue<T>(string key, T defaultValue = default(T));

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        void SetValue<T>(string key, T value);

        /// <summary>
        /// 获取配置节
        /// </summary>
        /// <typeparam name="T">配置节类型</typeparam>
        /// <param name="sectionName">节名称</param>
        /// <returns>配置节对象</returns>
        T GetSection<T>(string sectionName) where T : class, new();

        /// <summary>
        /// 设置配置节
        /// </summary>
        /// <typeparam name="T">配置节类型</typeparam>
        /// <param name="sectionName">节名称</param>
        /// <param name="section">配置节对象</param>
        void SetSection<T>(string sectionName, T section) where T : class;

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        bool ContainsKey(string key);

        /// <summary>
        /// 删除配置键
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否删除成功</returns>
        bool RemoveKey(string key);

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键集合</returns>
        IEnumerable<string> GetAllKeys();

        /// <summary>
        /// 保存配置
        /// </summary>
        void Save();

        /// <summary>
        /// 重新加载配置
        /// </summary>
        void Reload();

        /// <summary>
        /// 清除所有配置
        /// </summary>
        void Clear();

        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;
    }

    /// <summary>
    /// 配置变更事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 变更的配置键
        /// </summary>
        public string Key { get; }

        /// <summary>
        /// 旧值
        /// </summary>
        public object? OldValue { get; }

        /// <summary>
        /// 新值
        /// </summary>
        public object? NewValue { get; }

        /// <summary>
        /// 变更类型
        /// </summary>
        public ConfigurationChangeType ChangeType { get; }

        public ConfigurationChangedEventArgs(string key, object? oldValue, object? newValue, ConfigurationChangeType changeType)
        {
            Key = key;
            OldValue = oldValue;
            NewValue = newValue;
            ChangeType = changeType;
        }
    }

    /// <summary>
    /// 配置变更类型
    /// </summary>
    public enum ConfigurationChangeType
    {
        /// <summary>
        /// 添加
        /// </summary>
        Added,

        /// <summary>
        /// 修改
        /// </summary>
        Modified,

        /// <summary>
        /// 删除
        /// </summary>
        Removed
    }
}
